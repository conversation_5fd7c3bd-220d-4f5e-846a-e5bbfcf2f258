import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input, Textarea } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Switch } from '../components/ui/switch';
import { Sheet, SheetContent, SheetTrigger } from '../components/ui/sheet';
import { Download, Palette, Edit3, Layout, Eye, Menu, Settings } from 'lucide-react';
import html2canvas from 'html2canvas';

// Types for the promo generator
interface TravelData {
  title: string;
  destination: string;
  date: string;
  price: string;
  currency: string;
  packageDetails: string;
  plan: string;
  inclusions: string;
  exclusions: string;
  overlays: string;
}

interface ElementStyle {
  fontFamily: string;
  fontSize: number;
  fontWeight: string;
  color: string;
  backgroundColor: string;
  padding: number;
  borderRadius: number;
  textAlign: string;
  opacity: number;
  textShadow?: string;
  boxShadow?: string;
  textTransform?: string;
  letterSpacing?: string;
  lineHeight?: string;
}

interface TemplateElement {
  id: string;
  type: string;
  content: string;
  field?: keyof TravelData;
  position: { x: number; y: number };
  size: { width: number; height: number };
  style: ElementStyle;
  locked?: boolean;
}

interface TemplateLayout {
  id: string;
  name: string;
  displaySize: { width: number; height: number };
  elements: TemplateElement[];
  backgroundStyle: {
    backgroundColor: string;
    backgroundImage?: string;
    overlay?: boolean;
    overlayOpacity?: number;
  };
}

// Default data
const defaultTravelData: TravelData = {
  title: "Exotic Bali Adventure",
  destination: "Bali, Indonesia",
  date: "Dec 15-22, 2024",
  price: "45,000",
  currency: "₹",
  packageDetails: "7 Days / 6 Nights",
  plan: "Explore beautiful beaches, ancient temples, and vibrant culture",
  inclusions: "✓ Flights ✓ Hotel ✓ Meals ✓ Transfers ✓ Guide",
  exclusions: "✗ Personal expenses ✗ Travel insurance ✗ Tips",
  overlays: "Early Bird Discount 20% OFF"
};

const defaultElementStyle: ElementStyle = {
  fontFamily: "Inter",
  fontSize: 16,
  fontWeight: "400",
  color: "#000000",
  backgroundColor: "transparent",
  padding: 8,
  borderRadius: 0,
  textAlign: "left",
  opacity: 1,
};

// Template formats
const templateFormats: TemplateLayout[] = [
  {
    id: "square",
    name: "Square\n(1080x1080px)",
    displaySize: { width: 1080, height: 1080 },
    elements: [
      {
        id: "title",
        type: "text",
        content: "Exotic Bali Adventure",
        field: "title",
        position: { x: 50, y: 100 },
        size: { width: 980, height: 80 },
        style: { ...defaultElementStyle, fontSize: 48, fontWeight: "700", color: "#FFFFFF", textAlign: "center" }
      },
      {
        id: "destination",
        type: "text",
        content: "Bali, Indonesia",
        field: "destination",
        position: { x: 50, y: 200 },
        size: { width: 980, height: 60 },
        style: { ...defaultElementStyle, fontSize: 32, fontWeight: "600", color: "#FFFFFF", textAlign: "center" }
      },
      {
        id: "price",
        type: "price",
        content: "₹45,000",
        field: "price",
        position: { x: 50, y: 300 },
        size: { width: 980, height: 80 },
        style: { ...defaultElementStyle, fontSize: 56, fontWeight: "800", color: "#FFD700", textAlign: "center" }
      },
      {
        id: "package-details",
        type: "text",
        content: "7 Days / 6 Nights",
        field: "packageDetails",
        position: { x: 50, y: 400 },
        size: { width: 980, height: 40 },
        style: { ...defaultElementStyle, fontSize: 24, fontWeight: "500", color: "#FFFFFF", textAlign: "center" }
      },
      {
        id: "inclusions",
        type: "text",
        content: "✓ Flights ✓ Hotel ✓ Meals ✓ Transfers ✓ Guide",
        field: "inclusions",
        position: { x: 50, y: 500 },
        size: { width: 980, height: 60 },
        style: { ...defaultElementStyle, fontSize: 20, fontWeight: "400", color: "#FFFFFF", textAlign: "center" }
      }
    ],
    backgroundStyle: {
      backgroundColor: "#1E40AF",
      backgroundImage: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=1080&h=1080&fit=crop",
      overlay: true,
      overlayOpacity: 0.4
    }
  },
  {
    id: "story",
    name: "Story\n(1080x1920px)",
    displaySize: { width: 1080, height: 1920 },
    elements: [
      {
        id: "title",
        type: "text",
        content: "Exotic Bali Adventure",
        field: "title",
        position: { x: 40, y: 100 },
        size: { width: 1000, height: 80 },
        style: { ...defaultElementStyle, fontSize: 48, fontWeight: "700", color: "#FFFFFF", textAlign: "center" }
      },
      {
        id: "destination",
        type: "text",
        content: "Bali, Indonesia",
        field: "destination",
        position: { x: 40, y: 200 },
        size: { width: 1000, height: 60 },
        style: { ...defaultElementStyle, fontSize: 32, fontWeight: "600", color: "#FFFFFF", textAlign: "center" }
      },
      {
        id: "price",
        type: "price",
        content: "₹45,000",
        field: "price",
        position: { x: 40, y: 300 },
        size: { width: 1000, height: 80 },
        style: { ...defaultElementStyle, fontSize: 56, fontWeight: "800", color: "#FFD700", textAlign: "center" }
      }
    ],
    backgroundStyle: {
      backgroundColor: "#1E40AF",
      backgroundImage: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=1080&h=1920&fit=crop",
      overlay: true,
      overlayOpacity: 0.5
    }
  }
];

export default function PromoGenerator() {
  const [travelData, setTravelData] = useState<TravelData>(defaultTravelData);
  const [currentLayout, setCurrentLayout] = useState<TemplateLayout>(templateFormats[0]);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [showGrid, setShowGrid] = useState(false);
  const [showGuidelines, setShowGuidelines] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const templateRef = React.useRef<HTMLDivElement>(null);

  const handleInputChange = (field: keyof TravelData, value: string) => {
    setTravelData((prev) => ({ ...prev, [field]: value }));
  };

  const handleLayoutChange = (layoutId: string) => {
    const layout = templateFormats.find((l) => l.id === layoutId);
    if (layout) {
      setCurrentLayout(layout);
      setSelectedElement(null);
    }
  };

  const handleElementStyleChange = (elementId: string, styleProperty: keyof ElementStyle, value: any) => {
    setCurrentLayout((prev) => ({
      ...prev,
      elements: prev.elements.map((el) =>
        el.id === elementId ? { ...el, style: { ...el.style, [styleProperty]: value } } : el,
      ),
    }));
  };

  const handleBackgroundStyleChange = (property: string, value: any) => {
    setCurrentLayout((prev) => ({
      ...prev,
      backgroundStyle: { ...prev.backgroundStyle, [property]: value },
    }));
  };

  const exportTemplate = async () => {
    if (templateRef.current) {
      setIsExporting(true);
      setSelectedElement(null);

      await new Promise(resolve => setTimeout(resolve, 100));

      try {
        const canvas = await html2canvas(templateRef.current, {
          scale: 2,
          useCORS: true,
          backgroundColor: currentLayout.backgroundStyle.backgroundColor,
          logging: false,
        });
        
        const link = document.createElement("a");
        link.download = `${travelData.title.replace(/\s+/g, "-").toLowerCase()}-${currentLayout.id}.png`;
        link.href = canvas.toDataURL("image/png");
        link.click();

      } catch (error) {
        console.error("Export failed:", error);
        alert("There was an error exporting the image. Please try again.");
      } finally {
        setIsExporting(false);
      }
    }
  };

  const selectedElementData = selectedElement ? currentLayout.elements.find((el) => el.id === selectedElement) : null;

  // Calculate responsive template size
  const getTemplateSize = () => {
    // Get container dimensions more accurately
    const containerWidth = Math.min(window.innerWidth - 80, 1200); // Max width for better UX
    const containerHeight = window.innerHeight - 200; // Account for header and padding
    
    const aspectRatio = currentLayout.displaySize.width / currentLayout.displaySize.height;
    
    // Calculate size based on container
    let width = Math.min(containerWidth * 0.8, currentLayout.displaySize.width * 0.8);
    let height = width / aspectRatio;
    
    // Ensure it fits in height
    if (height > containerHeight * 0.8) {
      height = containerHeight * 0.8;
      width = height * aspectRatio;
    }
    
    // Ensure minimum size
    const minWidth = 300;
    if (width < minWidth) {
      width = minWidth;
      height = width / aspectRatio;
    }
    
    return { 
      width: Math.floor(width), 
      height: Math.floor(height), 
      scale: width / currentLayout.displaySize.width 
    };
  };

  const templateSize = getTemplateSize();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">
                🎨 TripXplo Promo Generator
              </h1>
            </div>
            
            <div className="flex items-center gap-2 sm:gap-4">
              <Select value={currentLayout.id} onValueChange={handleLayoutChange}>
                <SelectTrigger className="w-24 sm:w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {templateFormats.map((layout) => (
                    <SelectItem key={layout.id} value={layout.id}>
                      {layout.name.split('\n')[0]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button 
                size="sm" 
                disabled={isExporting} 
                className="text-xs sm:text-sm"
                onClick={() => exportTemplate()}
              >
                <Download className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{isExporting ? "Exporting..." : "Export"}</span>
                <span className="sm:hidden">{isExporting ? "..." : "Export"}</span>
              </Button>

              {/* Mobile Menu Button */}
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm" className="lg:hidden">
                    <Menu className="w-4 h-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-80">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">Settings</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="h-8 w-8 p-0"
                      >
                        <span className="sr-only">Close</span>
                        ×
                      </Button>
                    </div>
                    
                    <div className="space-y-6">
                      {/* Template Settings */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Template Settings</h4>
                        <div className="space-y-4">
                          <div>
                            <Label className="text-sm">Template Format</Label>
                            <Select value={currentLayout.id} onValueChange={handleLayoutChange}>
                              <SelectTrigger className="w-full mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {templateFormats.map((layout) => (
                                  <SelectItem key={layout.id} value={layout.id}>
                                    {layout.name.split('\n')[0]}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div>
                            <Label className="text-sm">Background Color</Label>
                            <div className="flex gap-2 mt-1">
                              <Input
                                type="color"
                                value={currentLayout.backgroundStyle.backgroundColor}
                                onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                                className="w-12 h-10 p-1 border rounded"
                              />
                              <Input
                                value={currentLayout.backgroundStyle.backgroundColor}
                                onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                                placeholder="#ffffff"
                                className="flex-1 text-sm"
                              />
                            </div>
                          </div>
                          
                          <div>
                            <Label className="flex items-center justify-between text-sm">
                              Background Overlay
                              <Switch
                                checked={currentLayout.backgroundStyle.overlay}
                                onCheckedChange={(checked) => handleBackgroundStyleChange("overlay", checked)}
                              />
                            </Label>
                            {currentLayout.backgroundStyle.overlay && (
                              <div className="mt-2">
                                <Label className="text-sm">Overlay Opacity</Label>
                                <Input
                                  type="range"
                                  min="0"
                                  max="1"
                                  step="0.1"
                                  value={currentLayout.backgroundStyle.overlayOpacity?.toString() || ''}
                                  onChange={(e) => handleBackgroundStyleChange("overlayOpacity", Number.parseFloat(e.target.value))}
                                  className="mt-1"
                                />
                                <span className="text-xs text-gray-500">{Math.round((currentLayout.backgroundStyle.overlayOpacity || 0) * 100)}%</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Display Settings */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Display Settings</h4>
                        <div className="space-y-4">
                          <div>
                            <Label className="flex items-center justify-between text-sm">
                              Show Grid
                              <Switch
                                checked={showGrid}
                                onCheckedChange={setShowGrid}
                              />
                            </Label>
                          </div>
                          <div>
                            <Label className="flex items-center justify-between text-sm">
                              Show Guidelines
                              <Switch
                                checked={showGuidelines}
                                onCheckedChange={setShowGuidelines}
                              />
                            </Label>
                          </div>
                        </div>
                      </div>

                      {/* Export Settings */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Export</h4>
                        <Button
                          onClick={() => {
                            exportTemplate();
                            setIsMobileMenuOpen(false);
                          }}
                          disabled={isExporting}
                          className="w-full"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          {isExporting ? "Exporting..." : "Export Image"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>

              {/* Desktop Settings Button */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm" className="hidden lg:flex">
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-96">
                  <div className="space-y-6">
                                         <div className="flex items-center justify-between">
                       <h3 className="text-lg font-semibold">Settings</h3>
                     </div>
                    
                    <div className="space-y-6">
                      {/* Template Settings */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Template Settings</h4>
                        <div className="space-y-4">
                          <div>
                            <Label className="text-sm">Template Format</Label>
                            <Select value={currentLayout.id} onValueChange={handleLayoutChange}>
                              <SelectTrigger className="w-full mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {templateFormats.map((layout) => (
                                  <SelectItem key={layout.id} value={layout.id}>
                                    {layout.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div>
                            <Label className="text-sm">Background Color</Label>
                            <div className="flex gap-2 mt-1">
                              <Input
                                type="color"
                                value={currentLayout.backgroundStyle.backgroundColor}
                                onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                                className="w-12 h-10 p-1 border rounded"
                              />
                              <Input
                                value={currentLayout.backgroundStyle.backgroundColor}
                                onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                                placeholder="#ffffff"
                                className="flex-1"
                              />
                            </div>
                          </div>
                          
                          <div>
                            <Label className="flex items-center justify-between text-sm">
                              Background Overlay
                              <Switch
                                checked={currentLayout.backgroundStyle.overlay}
                                onCheckedChange={(checked) => handleBackgroundStyleChange("overlay", checked)}
                              />
                            </Label>
                            {currentLayout.backgroundStyle.overlay && (
                              <div className="mt-2">
                                <Label className="text-sm">Overlay Opacity</Label>
                                <Input
                                  type="range"
                                  min="0"
                                  max="1"
                                  step="0.1"
                                  value={currentLayout.backgroundStyle.overlayOpacity?.toString() || ''}
                                  onChange={(e) => handleBackgroundStyleChange("overlayOpacity", Number.parseFloat(e.target.value))}
                                  className="mt-1"
                                />
                                <span className="text-sm text-gray-500">{Math.round((currentLayout.backgroundStyle.overlayOpacity || 0) * 100)}%</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Display Settings */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Display Settings</h4>
                        <div className="space-y-4">
                          <div>
                            <Label className="flex items-center justify-between text-sm">
                              Show Grid
                              <Switch
                                checked={showGrid}
                                onCheckedChange={setShowGrid}
                              />
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">Display alignment grid for precise positioning</p>
                          </div>
                          <div>
                            <Label className="flex items-center justify-between text-sm">
                              Show Guidelines
                              <Switch
                                checked={showGuidelines}
                                onCheckedChange={setShowGuidelines}
                              />
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">Show center guidelines for balanced layout</p>
                          </div>
                        </div>
                      </div>

                      {/* Export Settings */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Export Settings</h4>
                        <div className="space-y-4">
                          <Button
                            onClick={() => exportTemplate()}
                            disabled={isExporting}
                            className="w-full"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            {isExporting ? "Exporting..." : "Export High-Quality Image"}
                          </Button>
                          <p className="text-xs text-gray-500">
                            Exports as PNG with 2x resolution for crisp social media posts
                          </p>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div>
                        <h4 className="text-sm font-medium mb-3">Quick Actions</h4>
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setShowGrid(true);
                              setShowGuidelines(true);
                            }}
                            className="w-full justify-start"
                          >
                            <Layout className="w-4 h-4 mr-2" />
                            Show All Guides
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setShowGrid(false);
                              setShowGuidelines(false);
                            }}
                            className="w-full justify-start"
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            Hide All Guides
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="px-2 sm:px-4 lg:px-6 py-4 lg:py-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 lg:gap-6">
            
            {/* Travel Information - Left Sidebar */}
            <div className="lg:col-span-3 order-1 lg:order-1">
              <div className="lg:sticky lg:top-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
                      <Edit3 className="w-4 h-4 sm:w-5 sm:h-5" />
                      Travel Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 sm:space-y-4">
                    <div>
                      <Label htmlFor="title" className="text-sm">Package Title</Label>
                      <Input
                        id="title"
                        value={travelData.title}
                        onChange={(e) => handleInputChange("title", e.target.value)}
                        placeholder="Enter package title"
                        className="text-sm"
                      />
                    </div>

                    <div>
                      <Label htmlFor="destination" className="text-sm">Destination</Label>
                      <Input
                        id="destination"
                        value={travelData.destination}
                        onChange={(e) => handleInputChange("destination", e.target.value)}
                        placeholder="Enter destination"
                        className="text-sm"
                      />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      <div>
                        <Label htmlFor="date" className="text-sm">Travel Date</Label>
                        <Input
                          id="date"
                          value={travelData.date}
                          onChange={(e) => handleInputChange("date", e.target.value)}
                          placeholder="Enter dates"
                          className="text-sm"
                        />
                      </div>
                      <div>
                        <Label htmlFor="price" className="text-sm">Price</Label>
                        <div className="flex gap-2">
                          <Select
                            value={travelData.currency}
                            onValueChange={(value) => handleInputChange("currency", value)}
                          >
                            <SelectTrigger className="w-[80px] sm:w-[100px] text-xs sm:text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="₹">₹ (INR)</SelectItem>
                              <SelectItem value="$">$ (USD)</SelectItem>
                              <SelectItem value="€">€ (EUR)</SelectItem>
                              <SelectItem value="£">£ (GBP)</SelectItem>
                            </SelectContent>
                          </Select>
                          <Input
                            id="price"
                            value={travelData.price}
                            onChange={(e) => handleInputChange("price", e.target.value)}
                            placeholder="Enter price"
                            className="flex-1 text-sm"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="packageDetails" className="text-sm">Package Details</Label>
                      <Input
                        id="packageDetails"
                        value={travelData.packageDetails}
                        onChange={(e) => handleInputChange("packageDetails", e.target.value)}
                        placeholder="e.g., 7 Days / 6 Nights"
                        className="text-sm"
                      />
                    </div>

                    <div>
                      <Label htmlFor="plan" className="text-sm">Travel Plan</Label>
                      <Textarea
                        id="plan"
                        value={travelData.plan}
                        onChange={(e) => handleInputChange("plan", e.target.value)}
                        placeholder="Brief description of the travel plan"
                        rows={2}
                        className="text-sm"
                      />
                    </div>

                    <div>
                      <Label htmlFor="inclusions" className="text-sm">Inclusions</Label>
                      <Textarea
                        id="inclusions"
                        value={travelData.inclusions}
                        onChange={(e) => handleInputChange("inclusions", e.target.value)}
                        placeholder="What's included in the package"
                        rows={2}
                        className="text-sm"
                      />
                    </div>

                    <div>
                      <Label htmlFor="exclusions" className="text-sm">Exclusions</Label>
                      <Textarea
                        id="exclusions"
                        value={travelData.exclusions}
                        onChange={(e) => handleInputChange("exclusions", e.target.value)}
                        placeholder="What's not included"
                        rows={2}
                        className="text-sm"
                      />
                    </div>

                    <div>
                      <Label htmlFor="overlays" className="text-sm">Special Offers</Label>
                      <Input
                        id="overlays"
                        value={travelData.overlays}
                        onChange={(e) => handleInputChange("overlays", e.target.value)}
                        placeholder="Special offers or highlights"
                        className="text-sm"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Template Preview - Center */}
            <div className="lg:col-span-6 order-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 sm:pb-6">
                  <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
                    <Layout className="w-4 h-4 sm:w-5 sm:h-5" />
                    Template Designer
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant={showGrid ? "default" : "outline"}
                      size="sm"
                      onClick={() => setShowGrid(!showGrid)}
                      className="h-7 sm:h-8 px-2 sm:px-3 text-xs sm:text-sm hidden lg:flex"
                    >
                      Grid
                    </Button>
                    <Button
                      variant={showGuidelines ? "default" : "outline"}
                      size="sm"
                      onClick={() => setShowGuidelines(!showGuidelines)}
                      className="h-7 sm:h-8 px-2 sm:px-3 text-xs sm:text-sm hidden lg:flex"
                    >
                      Guides
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-2 sm:p-4 lg:p-6">
                  <div className="flex justify-center">
                    <div className="overflow-auto max-w-full">
                      <div
                        ref={templateRef}
                        className="relative shadow-lg overflow-hidden mx-auto"
                        style={{
                          width: `${templateSize.width}px`,
                          height: `${templateSize.height}px`,
                          backgroundColor: currentLayout.backgroundStyle.backgroundColor,
                          backgroundImage: currentLayout.backgroundStyle.backgroundImage
                            ? `url(${currentLayout.backgroundStyle.backgroundImage})`
                            : "none",
                          backgroundSize: "cover",
                          backgroundPosition: "center",
                        }}
                        onClick={() => setSelectedElement(null)}
                      >
                        {/* Background overlay */}
                        {currentLayout.backgroundStyle.overlay && currentLayout.backgroundStyle.backgroundImage && (
                          <div
                            className="absolute inset-0 bg-black"
                            style={{ opacity: currentLayout.backgroundStyle.overlayOpacity }}
                          />
                        )}

                        {/* Render all elements */}
                        {currentLayout.elements.map((element) => {
                          const content = element.field && travelData[element.field] ? travelData[element.field] : element.content;
                          if (!content) return null;
                          
                          return (
                            <div
                              key={element.id}
                              className={`absolute cursor-pointer ${selectedElement === element.id ? 'ring-2 ring-blue-500' : ''}`}
                              style={{
                                left: element.position.x * templateSize.scale,
                                top: element.position.y * templateSize.scale,
                                width: element.size.width * templateSize.scale,
                                height: element.size.height * templateSize.scale,
                                fontFamily: element.style.fontFamily,
                                fontSize: element.style.fontSize * templateSize.scale,
                                fontWeight: element.style.fontWeight,
                                color: element.style.color,
                                backgroundColor: element.style.backgroundColor,
                                padding: element.style.padding * templateSize.scale,
                                borderRadius: element.style.borderRadius * templateSize.scale,
                                textAlign: element.style.textAlign as any,
                                opacity: element.style.opacity,
                                display: 'flex',
                                alignItems: element.type === "price" ? "center" : "flex-start",
                                justifyContent: element.style.textAlign === "center" ? "center" : element.style.textAlign === "right" ? "flex-end" : "flex-start",
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedElement(element.id);
                              }}
                            >
                              {element.type === "price" && !content.match(/^[₹$€£¥A\$C\$]/) ? (travelData.currency || "₹") : ""}{content}
                            </div>
                          );
                        })}

                        {/* Grid Overlay */}
                        {!isExporting && showGrid && (
                          <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 10 }}>
                            <svg width="100%" height="100%" className="opacity-30">
                              <defs>
                                <pattern
                                  id="grid"
                                  width="20"
                                  height="20"
                                  patternUnits="userSpaceOnUse"
                                >
                                  <path
                                    d="M 20 0 L 0 0 0 20"
                                    fill="none"
                                    stroke="#3b82f6"
                                    strokeWidth="0.5"
                                  />
                                </pattern>
                              </defs>
                              <rect width="100%" height="100%" fill="url(#grid)" />
                            </svg>
                          </div>
                        )}

                        {/* Center Guidelines */}
                        {!isExporting && showGuidelines && (
                          <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 11 }}>
                            <div
                              className="absolute bg-pink-400 opacity-40"
                              style={{
                                left: "50%",
                                top: 0,
                                width: "1px",
                                height: "100%",
                                transform: "translateX(-50%)",
                              }}
                            />
                            <div
                              className="absolute bg-pink-400 opacity-40"
                              style={{
                                top: "50%",
                                left: 0,
                                height: "1px",
                                width: "100%",
                                transform: "translateY(-50%)",
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Template Settings - Right Sidebar */}
            <div className="lg:col-span-3 order-3">
              <div className="lg:sticky lg:top-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
                      <Palette className="w-4 h-4 sm:w-5 sm:h-5" />
                      {selectedElement ? `Customize ${selectedElementData?.type}` : "Template Settings"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 sm:space-y-4">
                    {selectedElement && selectedElementData ? (
                      <div className="space-y-3 sm:space-y-4">
                        <div>
                          <Label className="text-sm">Font Size</Label>
                          <Input
                            type="range"
                            min="8"
                            max="48"
                            value={selectedElementData.style.fontSize.toString()}
                            onChange={(e) =>
                              handleElementStyleChange(selectedElement, "fontSize", Number.parseInt(e.target.value))
                            }
                            className="mt-2"
                          />
                          <span className="text-xs sm:text-sm text-gray-500">{selectedElementData.style.fontSize}px</span>
                        </div>

                        <div>
                          <Label className="text-sm">Text Color</Label>
                          <div className="flex gap-2 mt-2">
                            <Input
                              type="color"
                              value={selectedElementData.style.color}
                              onChange={(e) => handleElementStyleChange(selectedElement, "color", e.target.value)}
                              className="w-10 h-8 sm:w-12 sm:h-10 p-1 border rounded"
                            />
                            <Input
                              value={selectedElementData.style.color}
                              onChange={(e) => handleElementStyleChange(selectedElement, "color", e.target.value)}
                              placeholder="#000000"
                              className="text-sm"
                            />
                          </div>
                        </div>

                        <div>
                          <Label className="text-sm">Background Color</Label>
                          <div className="flex gap-2 mt-2">
                            <Input
                              type="color"
                              value={selectedElementData.style.backgroundColor}
                              onChange={(e) =>
                                handleElementStyleChange(selectedElement, "backgroundColor", e.target.value)
                              }
                              className="w-10 h-8 sm:w-12 sm:h-10 p-1 border rounded"
                            />
                            <Input
                              value={selectedElementData.style.backgroundColor}
                              onChange={(e) =>
                                handleElementStyleChange(selectedElement, "backgroundColor", e.target.value)
                              }
                              placeholder="transparent"
                              className="text-sm"
                            />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3 sm:space-y-4">
                        <div>
                          <Label className="text-sm">Background Color</Label>
                          <div className="flex gap-2 mt-2">
                            <Input
                              type="color"
                              value={currentLayout.backgroundStyle.backgroundColor}
                              onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                              className="w-10 h-8 sm:w-12 sm:h-10 p-1 border rounded"
                            />
                            <Input
                              value={currentLayout.backgroundStyle.backgroundColor}
                              onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                              placeholder="#ffffff"
                              className="text-sm"
                            />
                          </div>
                        </div>

                        <div>
                          <Label className="flex items-center justify-between text-sm">
                            Background Overlay
                            <Switch
                              checked={currentLayout.backgroundStyle.overlay}
                              onCheckedChange={(checked) => handleBackgroundStyleChange("overlay", checked)}
                            />
                          </Label>
                          {currentLayout.backgroundStyle.overlay && (
                            <div className="mt-2">
                              <Label className="text-sm">Overlay Opacity</Label>
                              <Input
                                type="range"
                                min="0"
                                max="1"
                                step="0.1"
                                value={currentLayout.backgroundStyle.overlayOpacity?.toString() || ''}
                                onChange={(e) => handleBackgroundStyleChange("overlayOpacity", Number.parseFloat(e.target.value))}
                              />
                              <span className="text-xs sm:text-sm text-gray-500">{Math.round((currentLayout.backgroundStyle.overlayOpacity || 0) * 100)}%</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 