import React, { useState, useEffect } from 'react';

interface SheetProps {
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface SheetTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
}

interface SheetContentProps {
  children: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
  className?: string;
}

export function Sheet({ children, open, onOpenChange }: SheetProps) {
  const [isOpen, setIsOpen] = useState(open);

  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  const handleOpenChange = (newOpen: boolean) => {
    setIsOpen(newOpen);
    onOpenChange?.(newOpen);
  };

  return (
    <div className="relative">
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          if (child.type === SheetTrigger) {
            return React.cloneElement(child as any, {
              onClick: () => handleOpenChange(true)
            });
          }
          if (child.type === SheetContent && isOpen) {
            return React.cloneElement(child as any, {
              onClose: () => handleOpenChange(false)
            });
          }
        }
        return child;
      })}
    </div>
  );
}

export function SheetTrigger({ children, asChild = false }: SheetTriggerProps) {
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as any, {
      onClick: (e: any) => {
        e?.preventDefault?.();
        // The onClick will be handled by the parent Sheet component
      }
    });
  }
  return <>{children}</>;
}

export function SheetContent({ children, side = 'right', className = '', onClose }: SheetContentProps & { onClose?: () => void }) {
  const sideClasses = {
    top: 'inset-x-0 top-0 border-b',
    right: 'inset-y-0 right-0 h-full border-l',
    bottom: 'inset-x-0 bottom-0 border-t',
    left: 'inset-y-0 left-0 h-full border-r'
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-[90] bg-black/50"
        onClick={onClose}
      />
      {/* Content */}
      <div
        className={`fixed z-[91] bg-white p-6 shadow-lg transition-transform dark:bg-gray-800 ${sideClasses[side]} ${className}`}
        style={{
          width: side === 'left' || side === 'right' ? '400px' : 'auto',
          height: side === 'top' || side === 'bottom' ? 'auto' : '100%'
        }}
      >
        {children}
      </div>
    </>
  );
} 